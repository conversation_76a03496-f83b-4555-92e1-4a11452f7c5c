import { marked } from 'marked';
import html2canvas from 'html2canvas';
import { useUserStore } from './UserCenter/userStore';
import log from 'loglevel';

const BACKEND_SRV_URL = import.meta.env.VITE_BACKEND_SRV_URL;
log.setLevel('info');

marked.setOptions({
    breaks: true,       // 转换 \n 为 <br>
    gfm: true,          // 支持 GitHub Flavored Markdown
    headerIds: false,    // 禁用自动生成 header IDs
    mangle: false,
    sanitize: false, // 允许 HTML 标签
});

export default {
    data() {
        return {
            contents: [],
            mapInstances: [],
            showBtn: true,
            AMap: null,
            loading: false,
            errorMessage: '',
            s_address: null,
            e_address: null,
            last_end: '',
            startDate: null,
            dates: 3, // 设置默认值
            plan_mode: '往返', // 设置默认值
            travel_mode: '自驾', // 设置默认值
            // 滚动控制相关
            autoScrollEnabled: true,
            userScrollTimeout: null,
            lastScrollTop: 0,
            isUserScrolling: false,
            scrollObserver: null,
            // 表单数据持久化标识
            formDataKey: 'topmeans_form_data',
        }
    },
    async mounted() {
        // 确保只在客户端执行
        if (typeof window === 'undefined') return;

        let sc = '';
        let sk = '';

        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/amap_keys`, {
                method: 'GET',
                credentials: 'include'
            });
            if (!response.ok) {
                log.error('获取API密钥失败，请检查网络连接', response);
                throw new Error('获取API密钥失败，请检查网络连接');
            }

            const { AMAP_CODE, AMAP_KEY } = await response.json();
            sc = AMAP_CODE;
            sk = AMAP_KEY;
        } catch (err) {
            log.error('获取API密钥异常，请检查网络连接', err);
        }

        window._AMapSecurityConfig = { securityJsCode: sc };

        try {
            // 1. 异步加载地图脚本
            this.AMap = await this.loadAMapScript(sk);

            this.AMap.plugin(['AMap.AutoComplete'], () => {
                var sauto = new AMap.AutoComplete({
                    input: "start-tipinput"
                });

                sauto.on("select", (e) => {
                    this.s_address = e.poi.name;
                })

                var eauto = new AMap.AutoComplete({
                    input: "end-tipinput"
                });

                eauto.on("select", (e) => {
                    this.e_address = e.poi.name;
                })
            });
        } catch (err) {
            alert(err);
        }

        // 初始化滚动监听器
        this.initScrollListener();

        // 恢复表单数据
        this.loadFormData();
    },
    beforeUnmount() {
        // 清理滚动监听器
        this.cleanupScrollListener();
    },
    watch: {
        contents: {
            handler(newVal) {
                if (newVal.length > 0 && this.autoScrollEnabled) {
                    this.$nextTick(() => {
                        this.smartScrollToContent();
                    });
                }
            },
            deep: true
        },
        // 监听表单数据变化，自动保存到本地存储
        s_address: {
            handler(newVal) {
                this.saveFormData();
            }
        },
        e_address: {
            handler(newVal) {
                this.saveFormData();
            }
        },
        startDate: {
            handler(newVal) {
                this.saveFormData();
            }
        },
        dates: {
            handler(newVal) {
                this.saveFormData();
            }
        },
        plan_mode: {
            handler(newVal) {
                this.saveFormData();
            }
        },
        travel_mode: {
            handler(newVal) {
                this.saveFormData();
            }
        }
    },
    methods: {
        parseMarkdown(text) {
            return marked.parse(text || '');
        },
        // 初始化滚动监听器
        initScrollListener() {
            if (typeof window === 'undefined') return;

            // 监听用户滚动
            this.handleUserScroll = this.throttle(() => {
                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const scrollDiff = Math.abs(currentScrollTop - this.lastScrollTop);

                // 检测用户是否主动滚动（滚动幅度较大且不是自动滚动触发的）
                if (scrollDiff > 50 && this.autoScrollEnabled) {
                    // 检查是否是向上滚动（用户查看之前的内容）
                    const isScrollingUp = currentScrollTop < this.lastScrollTop;

                    // 如果是向上滚动或大幅度滚动，认为是用户主动操作
                    if (isScrollingUp || scrollDiff > 200) {
                        this.isUserScrolling = true;
                        this.autoScrollEnabled = false;

                        // 清除之前的定时器
                        if (this.userScrollTimeout) {
                            clearTimeout(this.userScrollTimeout);
                        }

                        // 5秒后恢复自动滚动
                        this.userScrollTimeout = setTimeout(() => {
                            this.isUserScrolling = false;
                            // 检查是否所有内容都已完成，如果是则不恢复自动滚动
                            if (!this.checkAllContentCompleted()) {
                                this.autoScrollEnabled = true;
                                // 恢复时滚动到最新内容
                                this.$nextTick(() => {
                                    this.smartScrollToContent();
                                });
                            }
                        }, 5000);
                    }
                }

                this.lastScrollTop = currentScrollTop;
            }, 150);

            window.addEventListener('scroll', this.handleUserScroll, { passive: true });
        },

        // 清理滚动监听器
        cleanupScrollListener() {
            if (typeof window === 'undefined') return;

            if (this.handleUserScroll) {
                window.removeEventListener('scroll', this.handleUserScroll);
            }

            if (this.userScrollTimeout) {
                clearTimeout(this.userScrollTimeout);
            }
        },

        // 节流函数
        throttle(func, limit) {
            let inThrottle;
            return function () {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        },

        // 检查是否所有内容都已完成
        checkAllContentCompleted() {
            return this.showBtn;
        },

        // 智能滚动到内容
        smartScrollToContent() {
            if (!this.autoScrollEnabled || typeof window === 'undefined') return;

            // 检查是否所有内容都已完成，如果是则停止自动滚动
            if (this.checkAllContentCompleted()) {
                this.autoScrollEnabled = false;
                return;
            }

            try {
                // 找到最后一个有内容的 answer-area-container
                const containers = document.querySelectorAll('.answer-area-container');
                if (containers.length === 0) return;

                let targetContainer = null;
                let hasActiveLoading = false;

                // 从后往前找到第一个正在显示内容的容器（有加载指示器的）
                for (let i = containers.length - 1; i >= 0; i--) {
                    const container = containers[i];
                    const loadingIndicator = container.querySelector('.loading-indicator');

                    // 如果有加载指示器且可见，说明正在处理，这是我们要滚动到的目标
                    if (loadingIndicator &&
                        loadingIndicator.offsetParent !== null && // 检查元素是否可见
                        window.getComputedStyle(loadingIndicator).display !== 'none') {
                        targetContainer = container;
                        hasActiveLoading = true;
                        break;
                    }
                }

                // 如果没有找到正在处理的容器，检查是否真的全部完成了
                if (!hasActiveLoading) {
                    // 再次检查是否所有内容都已完成
                    if (this.checkAllContentCompleted()) {
                        this.autoScrollEnabled = false;
                        return;
                    }
                    // 如果还有未完成的内容，滚动到最后一个容器
                    if (containers.length > 0) {
                        targetContainer = containers[containers.length - 1];
                    }
                }

                if (targetContainer) {
                    // 滚动到容器底部附近，确保用户能看到正在生成的内容
                    const containerRect = targetContainer.getBoundingClientRect();
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                    // 计算目标滚动位置：容器底部 - 视窗高度的70%，确保容器在视窗中央偏下
                    const viewportHeight = window.innerHeight;
                    const targetScrollTop = scrollTop + containerRect.bottom - viewportHeight * 0.7;

                    // 平滑滚动到目标位置
                    window.scrollTo({
                        top: Math.max(0, targetScrollTop),
                        behavior: 'smooth'
                    });
                } else {
                    // 备用方案：滚动到页面底部
                    window.scrollTo({
                        top: document.body.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            } catch (error) {
                // 备用方案：滚动到页面底部
                window.scrollTo({
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                });
            }
        },

        // 保留原方法名以兼容其他调用
        scrollPageToBottom() {
            this.smartScrollToContent();
        },

        // 重置滚动状态（在开始新一轮操作时调用）
        resetScrollState() {
            this.autoScrollEnabled = true;
            this.isUserScrolling = false;

            // 安全地获取当前滚动位置
            if (typeof window !== 'undefined') {
                this.lastScrollTop = window.pageYOffset || document.documentElement.scrollTop || 0;
            } else {
                this.lastScrollTop = 0;
            }

            // 清除用户滚动定时器
            if (this.userScrollTimeout) {
                clearTimeout(this.userScrollTimeout);
                this.userScrollTimeout = null;
            }
        },

        // 保存表单数据到本地存储
        saveFormData() {
            if (typeof window === 'undefined') return;

            try {
                const formData = {
                    s_address: this.s_address,
                    e_address: this.e_address,
                    startDate: this.startDate,
                    dates: this.dates,
                    plan_mode: this.plan_mode,
                    travel_mode: this.travel_mode,
                    timestamp: Date.now() // 添加时间戳，用于数据有效性检查
                };

                localStorage.setItem(this.formDataKey, JSON.stringify(formData));
                console.log('表单数据已保存到本地存储');
            } catch (error) {
                console.warn('保存表单数据失败:', error);
            }
        },

        // 从本地存储加载表单数据
        loadFormData() {
            if (typeof window === 'undefined') return;

            try {
                const savedData = localStorage.getItem(this.formDataKey);
                if (!savedData) {
                    console.log('没有找到保存的表单数据');
                    return;
                }

                const formData = JSON.parse(savedData);

                // 检查数据有效性（可选：设置过期时间，比如7天）
                const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
                if (formData.timestamp && (Date.now() - formData.timestamp > maxAge)) {
                    console.log('保存的表单数据已过期，清除数据');
                    this.clearFormData();
                    return;
                }

                // 恢复表单数据
                if (formData.s_address !== undefined) {
                    this.s_address = formData.s_address;
                }
                if (formData.e_address !== undefined) {
                    this.e_address = formData.e_address;
                }
                if (formData.startDate !== undefined) {
                    this.startDate = formData.startDate;
                }
                if (formData.dates !== undefined) {
                    this.dates = formData.dates;
                }
                if (formData.plan_mode !== undefined) {
                    this.plan_mode = formData.plan_mode;
                }
                if (formData.travel_mode !== undefined) {
                    this.travel_mode = formData.travel_mode;
                }

                console.log('表单数据已从本地存储恢复');
            } catch (error) {
                console.warn('加载表单数据失败:', error);
                // 如果数据损坏，清除它
                this.clearFormData();
            }
        },

        // 清除本地存储的表单数据
        clearFormData() {
            if (typeof window === 'undefined') return;

            try {
                localStorage.removeItem(this.formDataKey);
                console.log('表单数据已清除');
            } catch (error) {
                console.warn('清除表单数据失败:', error);
            }
        },

        // 重置表单到默认值
        resetFormData() {
            this.s_address = null;
            this.e_address = null;
            this.startDate = null;
            this.dates = 3;
            this.plan_mode = '往返';
            this.travel_mode = '自驾';

            // 清除本地存储
            this.clearFormData();

            console.log('表单已重置到默认值');
        },
        async drivingPlanning(index, start, end) {
            try {
                // 确保地图容器已渲染
                await this.$nextTick();
                await this.$nextTick(); // 双重确保

                const container = document.getElementById(`map-container-${index}`);
                if (!container) {
                    log.error('地图容器未找到');
                    throw new Error('地图容器未找到');
                }

                await this.initMap(index, start, end);
            } catch (err) {
                log.error('路线生成错误', err);
                this.errorMessage = err.message || '路线生成失败';
            } finally {
                this.loading = false;
            }
        },
        // 存库
        async savePlanToDB(index, account, formattedDateTime) {
            // 保存截图
            await this.saveMapAsImage(index, account, formattedDateTime);

            let md_content = `# Smart Travel Plan\n\n## ${this.s_address} 到 ${this.e_address}\n\n`;
            if (this.contents[index]['rent']) {
                md_content += `${this.contents[index]['rent']}\n`;
            }

            md_content += `${this.contents[index]['plan']}\n`;
            md_content += `![路线规划](./map-${formattedDateTime}-${index}.png)\n`;

            for (let i = 0; i < this.contents[index]['view'].length; i++) {
                md_content += `${this.contents[index]['view'][i]['info']}\n`;
                if (this.contents[index]['view'][i]['url']) {
                    md_content += `![${this.contents[index]['view'][i]['name']}](${this.contents[index]['view'][i]['url']})\n`;
                }
            }

            for (let i = 0; i < this.contents[index]['hotel'].length; i++) {
                md_content += `${this.contents[index]['hotel'][i]['info']}\n`;
                if (this.contents[index]['hotel'][i]['url']) {
                    md_content += `![携程直达：${this.contents[index]['hotel'][i]['name']}](${this.contents[index]['hotel'][i]['url']})\n`;
                }
            }

            for (let i = 0; i < this.contents[index]['food'].length; i++) {
                md_content += `${this.contents[index]['food'][i]['info']}\n`;
                md_content += `![${this.contents[index]['food'][i]['name']}](${this.contents[index]['food'][i]['url']})\n`;
            }

            md_content += `${this.contents[index]['cost']}\n`;

            let response = await fetch(`${BACKEND_SRV_URL}/api/save_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: md_content,
                    user: account,
                    filename: `plan-${formattedDateTime}-${index}.md`, // 文件名
                })
            });
            if (!response.ok) {
                log.error('保存计划失败，请检查网络连接', response);
                throw new Error('保存计划失败，请检查网络连接');
            }

            // 存库
            response = await fetch(`${BACKEND_SRV_URL}/api/user/add_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    account: account,
                    create_time: formattedDateTime,
                    days: this.dates,
                })
            });
            if (!response.ok) {
                log.error('计划存库失败，请检查网络连接', response);
                throw new Error('计划存库失败，请检查网络连接');
            }
        },
        // 保存地图为图片
        async saveMapAsImage(index, account, formattedDateTime) {
            try {
                const mapContainer = document.getElementById(`map-container-${index}`);
                if (!mapContainer) {
                    alert('地图容器未找到');
                    return;
                }

                // 使用 html2canvas 截取地图容器
                const canvas = await html2canvas(mapContainer, {
                    useCORS: true,        // 启用跨域
                    allowTaint: true,     // 允许污染模式
                    logging: true,        // 开启日志查看问题
                    scale: 2,             // 提高分辨率
                    ignoreElements: (element) => {
                        // 忽略版权信息外的其他遮挡元素
                        return element.id === 'some-obstructive-element';
                    },
                });

                // 将 Canvas 转换为 Base64 数据
                const imageData = canvas.toDataURL('image/png');

                // 发送图片数据到后端
                const response = await fetch(`${BACKEND_SRV_URL}/api/save_amap_img`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image: imageData, // Base64 图片数据
                        user: account,
                        filename: `map-${formattedDateTime}-${index}.png` // 文件名
                    })
                });

                if (!response.ok) {
                    log.error('图片保存失败，请检查网络连接', response);
                    throw new Error('图片保存失败');
                }

                const result = await response.json();
            } catch (err) {
                log.error('保存地图为图片失败:', err);
            }
        },
        async getHotelUrl(hotelName, account, formattedDateTime, day) {
            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/hotel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: `${this.last_end}${hotelName.replace('*', '')}`,
                        user: account,
                        create_time: formattedDateTime,
                        day: `${day}`,
                    })
                });

                if (!response.ok) {
                    log.error('酒店信息获取失败，请检查网络连接', response);
                    throw new Error('酒店信息获取失败');
                }

                const { success, url } = await response.json();
                return url;
            } catch (err) {
                log.error('酒店信息获取失败:', err);
            }
        },
        async getFoodImgUrl(foodName, foodInfo) {
            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/ai_img`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: `一种食物或一个著名饭店，请根据后续描述来进行写实风格的图片生成，名字：${foodName},相关信息：${foodInfo}`
                    })
                });

                if (!response.ok) {
                    log.error('美食图片获取失败，请检查网络连接', response);
                    throw new Error('美食图片获取失败');
                }

                const { success, url } = await response.json();
                return url;
            } catch (err) {
                log.error('美食图片获取失败:', err);
            }
        },
        async getViewUrl(viewName) {
            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/view`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: viewName
                    })
                });

                if (!response.ok) {
                    log.error('景点信息获取失败，请检查网络连接', response);
                    throw new Error('景点信息获取失败');
                }

                const { success, url } = await response.json();
                return url;
            } catch (err) {
                log.error('景点信息获取失败:', err);
            }
        },
        async sendMessage(index, msg, formattedDateTime) {
            this.contents.push({
                rentCompleted: false,
                planCompleted: false,
                viewCompleted: false,
                hotelCompleted: false,
                foodCompleted: false,
                costCompleted: false
            });
            const us = useUserStore();
            const { success, user } = await us.getUserInfo();
            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/ds`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ msg })
                });
                if (!response.ok) {
                    log.error('DS API 请求失败!');
                    throw new Error('DS API 请求失败!');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let result = '';

                let rent = '';
                let start = '';
                let end = '';
                let distance = '';
                let serviceNum = '';
                let avoid = '';
                let vi = '';
                let ho = '';
                let fo = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    // 处理流式数据
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim());

                    for (const line of lines) {
                        try {
                            if (!line.startsWith('data: ')) continue;

                            const jsonStr = line.slice(6);
                            if (jsonStr === '[DONE]') break;

                            const data = JSON.parse(jsonStr);
                            const msg = data.choices[0].delta.content;

                            if (msg) {
                                result += msg;
                            }
                        } catch (err) {
                            log.error('解析数据失败:', err);
                        }

                        if (this.travel_mode === '租车') {
                            if (result.includes('#ST#') && rent === '') rent = result.split('#RT#')[1].split('#ST#')[0].trim();
                        }
                        if (result.includes('#EN#') && start === '') start = result.split('#ST#')[1].split('#EN#')[0].trim();
                        if (result.includes('#DI#') && end === '') {
                            end = result.split('#EN#')[1].split('#DI#')[0].trim();
                            this.contents[index]['amap'] = '';
                            // 等待地图容器加载
                            await this.$nextTick();

                            // 开始路线规划
                            try {
                                await this.drivingPlanning(index, start, end);
                            } catch (err) {
                                log.error('导航规划失败:', err);
                            }
                        }
                        if (result.includes('#SN#') && distance === '') distance = result.split('#DI#')[1].split('#SN#')[0];
                        if (result.includes('#AD#') && serviceNum === '') serviceNum = result.split('#SN#')[1].split('#AD#')[0];
                        if (result.includes('#VI#') && avoid === '') avoid = result.split('#AD#')[1].split('#VI#')[0];
                        if (result.includes('#HO#') && vi === '') vi = result.split('#VI#')[1].split('#HO#')[0];
                        if (result.includes('#FO#') && ho === '') ho = result.split('#HO#')[1].split('#FO#')[0];
                        if (result.includes('#CO#') && fo === '') fo = result.split('#FO#')[1].split('#CO#')[0];

                        // 将原始模板回答内容转为易读格式
                        // 租车建议只在第1天出现
                        if (index === 0 && this.travel_mode === '租车') {
                            if (result.includes('#RT#') && start === '' && !result.split('#RT#')[1].includes('#')) {
                                this.contents[index]['rent'] = `**租车建议**\n${result.split('#RT#')[1]}`;
                            }
                        }

                        if (result.includes('#ST#') && start === '' && !result.split('#ST#')[1].includes('#')) {
                            if (index === 0 && this.travel_mode === '租车' && rent !== '$$$') {
                                this.contents[index]['rent'] = `**租车建议**\n${rent}`;
                                rent = '$$$'; // 标记结束
                                // 标记租车建议完成
                                this.contents[index].rentCompleted = true;
                            }
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${result.split('#ST#')[1]}`;
                        }

                        if (result.includes('#EN#') && end === '' && !result.split('#EN#')[1].includes('#')) {
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${result.split('#EN#')[1]}`;
                        }

                        if (result.includes('#DI#') && distance === '' && !result.split('#DI#')[1].includes('#')) {
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${end}\n**里程：** ${result.split('#DI#')}`;
                        }

                        if (result.includes('#SN#') && serviceNum === '' && !result.split('#SN#')[1].includes('#')) {
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${end}\n**里程：** ${distance}\n**服务区：** ${result.split('#SN#')[1]}`;
                        }

                        if (result.includes('#AD#') && avoid === '' && !result.split('#AD#')[1].includes('#')) {
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${end}\n**里程：** ${distance}\n**服务区：** ${serviceNum}\n**避堵建议：** ${result.split('#AD#')[1]}`;
                        }

                        if (result.includes('#VI#') && vi === '' && !result.split('#VI#')[1].includes('#')) {
                            if (avoid !== '$$$') {
                                this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${end}\n**里程：** ${distance}\n**服务区：** ${serviceNum}\n**避堵建议：** ${avoid}`
                                avoid = '$$$'; // 标记结束
                                // 标记规划完成
                                this.contents[index].planCompleted = true;
                                this.contents[index]['view'] = [];
                            }

                            const vi_parts = result.split('#VI#')[1].split('*VINAME*');
                            for (let i = 0; i < vi_parts.length; i++) {
                                if (i === 0) continue; // 跳过第一个部分
                                if (i > this.contents[index]['view'].length) this.contents[index]['view'].push({});
                                this.contents[index]['view'][i - 1]['info'] = '**景点信息:** ';
                                if (vi_parts[i].includes('**景点信息:**')) {
                                    this.contents[index]['view'][i - 1]['info'] += vi_parts[i].split('**景点信息:**')[1].trim()
                                    this.contents[index]['view'][i - 1]['name'] = vi_parts[i].split('**景点信息:**')[0].trim().replace(/\*/g, '');
                                    if (!('url' in this.contents[index]['view'][i - 1])) {
                                        // 等待 5 秒，否则爬取图片容易被反制
                                        await new Promise(resolve => setTimeout(resolve, 5000));
                                        this.contents[index]['view'][i - 1]['url'] = await this.getViewUrl(this.contents[index]['view'][i - 1]['name']);
                                    }
                                }
                            }
                        }

                        if (result.includes('#HO#') && ho === '' && !result.split('#HO#')[1].includes('#')) {
                            if (vi !== '$$$') {
                                // this.contents[index]['view'] = `\n\n\n\n**沿途景点：**\n${vi}`;
                                vi = '$$$'; // 标记结束
                                // 标记景点推荐完成
                                this.contents[index].viewCompleted = true;
                                this.contents[index]['hotel'] = [];
                            }

                            const ho_parts = result.split('#HO#')[1].split('*HONAME*');
                            for (let i = 0; i < ho_parts.length; i++) {
                                if (i === 0) continue; // 跳过第一个部分
                                if (i > this.contents[index]['hotel'].length) this.contents[index]['hotel'].push({});
                                this.contents[index]['hotel'][i - 1]['info'] = '**酒店信息:** ';
                                if (ho_parts[i].includes('**酒店信息:**')) {
                                    this.contents[index]['hotel'][i - 1]['info'] += ho_parts[i].split('**酒店信息:**')[1].trim()
                                    this.contents[index]['hotel'][i - 1]['name'] = ho_parts[i].split('**酒店信息:**')[0].trim().replace(/\*/g, '');
                                    if (!('url' in this.contents[index]['hotel'][i - 1])) {
                                        this.contents[index]['hotel'][i - 1]['url'] = await this.getHotelUrl(this.contents[index]['hotel'][i - 1]['name'], user.account, formattedDateTime, index + 1);
                                    }
                                }
                            }
                        }

                        if (result.includes('#FO#') && fo === '' && !result.split('#FO#')[1].includes('#')) {
                            if (ho !== '$$$') {
                                ho = '$$$'; // 标记结束
                                // 标记酒店推荐完成
                                this.contents[index].hotelCompleted = true;
                                this.contents[index]['food'] = [];
                            }

                            const fo_parts = result.split('#FO#')[1].split('*FONAME*');
                            for (let i = 0; i < fo_parts.length; i++) {
                                if (i === 0) continue; // 跳过第一个部分
                                if (i > this.contents[index]['food'].length) this.contents[index]['food'].push({});
                                this.contents[index]['food'][i - 1]['info'] = '**美食信息:** ';
                                if (fo_parts[i].includes('**美食信息:**')) {
                                    this.contents[index]['food'][i - 1]['info'] += fo_parts[i].split('**美食信息:**')[1].trim()
                                    this.contents[index]['food'][i - 1]['name'] = fo_parts[i].split('**美食信息:**')[0].trim().replace(/\*/g, '');
                                }
                            }
                        }

                        if (result.includes('#CO#')) {
                            if (fo !== '$$$') {
                                for (let i = 0; i < this.contents[index]['food'].length; i++) {
                                    this.contents[index]['food'][i]['url'] = await this.getFoodImgUrl(this.contents[index]['food'][i]['name'], this.contents[index]['food'][i]['info']);
                                }
                                fo = '$$$'; // 标记结束
                                // 标记美食推荐完成
                                this.contents[index].foodCompleted = true;
                            }
                            this.contents[index]['cost'] = `**预估当日费用：**\n${result.split('#CO#')[1]}`;
                            // 标记费用计算完成
                            this.contents[index].costCompleted = true;
                        }
                    }
                }

                // 存库
                await this.savePlanToDB(index, user.account, formattedDateTime);
                return end;
            } catch (error) {
                log.error(`API请求失败: ${error}`);
            }
            return null;
        },
        // 处理操作按钮点击事件
        handleActionClick(type, index) {
            const typeNames = {
                'rent': '租车建议',
                'plan': '行程规划',
                'view': '景点推荐',
                'hotel': '住宿推荐',
                'food': '美食推荐',
                'cost': '费用预估'
            };

            alert(`您点击了第${index + 1}天的${typeNames[type]}操作按钮！\n\n这里可以添加更多功能，比如：\n- 编辑内容\n- 分享到社交媒体\n- 保存为收藏\n- 查看详细信息`);
        },
        validateFormData() {
            // 解构获取值
            const {
                s_address,
                e_address,
                startDate,
                dates
            } = this;

            // 地址有效性验证（兼容空字符串检测）
            const isAddressValid = [s_address, e_address].every(
                addr => typeof addr === 'string' && addr.trim().length >= 2
            );

            // 日期有效性验证
            const isDateValid = startDate !== null;

            // 数值范围验证（禁止0和负数）
            const isDurationValid = dates > 0 && dates <= 5;
            if (dates > 5) {
                alert('处于规划耗时，请勿一次性规划超过5天的行程，可以分多次规划');
            }

            return isAddressValid && isDateValid && isDurationValid;
        },
        async planning() {
            // 校验用户是否登录
            const us = useUserStore();
            if (!us.checkLoginStatus()) {
                alert('请先登录');
                return;
            }

            // 数据校验
            if (!this.validateFormData()) {
                alert('请输入完整内容,包括起点，终点，开始日期，游玩天数');
                return;
            };

            // 隐藏按钮
            this.showBtn = false;

            // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
            this.resetScrollState();

            // 状态初始化
            this.contents = [];

            // 获取时间
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDate = `${year}${month}${day}`;
            const formattedTime = `${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}${String(date.getSeconds()).padStart(2, '0')}`;
            const formattedDateTime = `${formattedDate}${formattedTime}`;

            let rent_prompt = '';
            let rent_answer_format = '';
            if (this.travel_mode === '租车') {
                rent_prompt = '请针对油车和电车分别给出三个租车车辆类型推荐，具体到品牌和车型，然后给出价位和靠谱的租车公司';
                rent_answer_format = '#RT# "油车和电车三个租车车辆类型推荐，具体到品牌和车型，然后给出价位和靠谱的租车公司"';
            }

            // 按照游玩天数进行多轮次询问
            for (let i = 1; i <= this.dates; i++) {
                // 从环境变量中获取 prompt 模板
                let promptTemplate = import.meta.env.VITE_TRAVEL_PLAN_PROMPT || '';

                // 替换模板中的变量
                promptTemplate = promptTemplate
                    .replace(/startDate/g, this.startDate)
                    .replace(/s_address/g, this.s_address)
                    .replace(/e_address/g, this.e_address)
                    .replace(/plan_mode/g, this.plan_mode)
                    .replace(/travel_mode/g, this.travel_mode)
                    .replace(/dates/g, this.dates)
                    .replace(/rent_prompt/g, rent_prompt)
                    .replace(/rent_answer_format/g, rent_answer_format)
                    .replace(/last_end/g, this.last_end);

                promptTemplate = promptTemplate.replace(/i/g, `${i}`);

                if (i > 1) {
                    promptTemplate += `，请注意，第${i - 1}天的规划已给出，终点是${this.last_end}，所以这次的起点应该是${this.last_end}`;
                }

                this.last_end = await this.sendMessage(i - 1, promptTemplate, formattedDateTime);
            }

            this.showBtn = true;
            // 所有内容生成完毕，不需要额外的滚动操作
        },
        // 封装地图脚本加载
        loadAMapScript(key) {
            return new Promise(async (resolve, reject) => {
                if (window.AMap) return resolve(window.AMap);

                try {
                    const response = await fetch(`${BACKEND_SRV_URL}/api/amap`, {
                        method: 'GET',
                        credentials: 'include'
                    });
                    if (!response.ok) {
                        throw new Error('地图脚本加载失败，请检查网络连接');
                    }

                    const { scriptUrl } = await response.json();

                    const script = document.createElement('script');
                    script.src = scriptUrl;
                    script.onload = () => resolve(window.AMap);
                    script.onerror = (err) => reject(
                        new Error('地图脚本加载失败，请检查网络连接', { cause: err })
                    );
                    document.head.appendChild(script);
                } catch (err) {
                    reject(new Error('地图脚本加载失败，请检查网络连接', { cause: err }));
                }
            });
        },

        // 封装地理编码操作
        async getGeocodePosition(geocoder, address) {
            return new Promise((resolve, reject) => {
                geocoder.getLocation(address, (status, result) => {
                    if (status === 'complete' && result.geocodes.length) {
                        resolve(result.geocodes[0].location);
                    } else {
                        reject(new Error(`无法解析地址: ${address}`));
                    }
                });
            });
        },

        // 重构后的地图初始化
        async initMap(index, start, end) {
            try {
                let map = new this.AMap.Map(`map-container-${index}`, {
                    renderer: "canvas",
                    resizeEnable: true,
                    viewMode: "2D",
                    crossOrigin: 'anonymous',
                    WebGLParams: {
                        preserveDrawingBuffer: true
                    }
                });

                this.mapInstances.push(map);

                //构造路线导航类
                let driving = new this.AMap.Driving({
                    map: map,
                    panel: "",  // 指定空字符串禁用默认面板
                    renderer: "canvas", // 使用Canvas绘制路线
                });

                // 根据起终点名称规划驾车导航路线
                driving.search([
                    { keyword: start },
                    { keyword: end }
                ], function (status, result) {
                    if (status === 'complete') {
                    } else {
                        log.error('获取驾车数据失败：' + result);
                    }
                });
            } catch (err) {
                log.error('地图初始化失败:', err);
                throw new Error(`地图初始化失败: ${err.message}`);
            }
        }
    }
}
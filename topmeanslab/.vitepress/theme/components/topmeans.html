<div class="travel-form">
    <!-- 旅游起点 -->
    <div class="form-item">
        <label for="start-tipinput" class="form-label">
            <span class="icon-ori">📍</span>旅游起点
        </label>
        <input id="start-tipinput" class="form-input" v-model="s_address" type="text" placeholder="请输入出发城市/地点">
    </div>

    <!-- 旅游终点 -->
    <div class="form-item">
        <label for="end-tipinput" class="form-label">
            <span class="icon-ori">🏁</span>旅游终点
        </label>
        <input id="end-tipinput" class="form-input" v-model="e_address" type="text" placeholder="请输入目的地城市/景点">
    </div>

    <div class="option-area">
        <!-- 日期与天数行 -->
        <div class="form-row">
            <!-- 出发日期 -->
            <div class="form-item">
                <label for="start-dateinput" class="form-label">
                    <span class="icon-ori">📅</span>
                    出发日期
                </label>
                <input type="date" class="vp-date-input" v-model="startDate">
            </div>
        </div>

        <!-- 游玩天数 -->
        <div class="travel-days">
            <div class="form-item">
                <label for="start-dateinput" class="form-label">
                    <span class="icon-ori">⏳</span>
                    天数
                </label>
                <select id="datesinput" class="form-day-input" v-model.number="dates">
                    <option v-for="n in 5" :value="n" :key="n" :selected="n === 3">{{ n }}</option>
                </select>
            </div>
        </div>

        <!-- 规划模式 -->
        <div class="plan-mode">
            <div class="form-item">
                <label for="plan-mode" class="form-label">
                    <span class="icon-ori">🔍</span>
                    模式
                </label>
                <select id="plan-mode" class="form-plan-input" v-model="plan_mode">
                    <option value="往返">往返</option>
                    <option value="单程">单程</option>
                </select>
            </div>
        </div>

        <!-- 交通方式 -->
        <div class="travel-mode">
            <div class="form-item">
                <label for="plan-mode" class="form-label">
                    <span class="icon-ori">🚗</span>
                    交通
                </label>
                <select id="plan-mode" class="form-travel-input" v-model="travel_mode">
                    <option value="自驾">自驾</option>
                    <option value="租车">租车</option>
                </select>
            </div>
        </div>
    </div>
    <button v-show="showBtn" class="btn-planning" @click="planning">
        <span class="icon">立即出发</span>
    </button>
    <div class="btn-planning" v-show="!showBtn">
        <span class="loading-circle"></span>
        规划中,请稍候...
    </div>

    <!-- 旅游规划结果展示区，动态生成 -->
    <div id="scroll-area" class="scroll-container" ref="scrollContainer">
        <div v-for="(content, index) in contents" :key="'day-' + index">
            <div data-dynamic-item class="answer-area-container" v-show="'rent' in content">
                <div class="loading-indicator" v-show="!content.rentCompleted">
                    <div class="spinner"></div>
                    <span class="loading-text">处理中，请稍候...</span>
                </div>
                <div class="answer-area" v-html="parseMarkdown(content.rent)"></div>
                <button class="answer-action-btn" v-show="content.rentCompleted" @click="handleActionClick('rent', index)">
                    <span class="btn-icon">💡</span>
                    <span class="btn-text">操作</span>
                </button>
            </div>
            <div data-dynamic-item class="answer-area-container" v-show="'plan' in content">
                <div class="loading-indicator" v-show="!content.planCompleted">
                    <div class="spinner"></div>
                    <span class="loading-text">处理中，请稍候...</span>
                </div>
                <div class="answer-area" v-html="parseMarkdown(content.plan)"></div>
                <button class="answer-action-btn" v-show="content.planCompleted" @click="handleActionClick('plan', index)">
                    <span class="btn-icon">🗺️</span>
                    <span class="btn-text">操作</span>
                </button>
            </div>
            <div class="planning-box" v-if="'amap' in content">
                <div :id="`map-container-${index}`" class="map-container"></div>
            </div>
            <div data-dynamic-item class="answer-area-container" v-show="'view' in content">
                <div class="loading-indicator" v-show="!content.viewCompleted">
                    <div class="spinner"></div>
                    <span class="loading-text">处理中，请稍候...</span>
                </div>
                <div class="answer-area">
                    <div class="answer-area" v-html="this.parseMarkdown('**景点推荐:**\n\n')"></div>
                    <div class="answer-area" v-for="(view, viewIndex) in content.view" :key="'view-' + viewIndex">
                        <span class="icon-ori">🏞️</span>
                        {{ view.name }}
                        <img :src="view.url" alt="景点图片" class="view-image">
                        <div class="answer-area" v-html="this.parseMarkdown(view.info)"></div>
                    </div>
                </div>
                <button class="answer-action-btn" v-show="content.viewCompleted" @click="handleActionClick('view', index)">
                    <span class="btn-icon">🏞️</span>
                    <span class="btn-text">操作</span>
                </button>
            </div>
            <div data-dynamic-item class="answer-area-container" v-show="'hotel' in content">
                <div class="loading-indicator" v-show="!content.hotelCompleted">
                    <div class="spinner"></div>
                    <span class="loading-text">处理中，请稍候...</span>
                </div>
                <div class="answer-area">
                    <div class="answer-area" v-html="this.parseMarkdown('**住宿推荐:**\n\n')"></div>
                    <div class="answer-area" v-for="(hotel, hotelIndex) in content.hotel" :key="'hotel-' + hotelIndex">
                        <a :href="hotel.url" target="_blank" class="hotel-link">
                            <span class="icon-ori">🏨</span>
                            {{ `携程直达：${hotel.name}` }}
                        </a>
                        <div class="answer-area" v-html="this.parseMarkdown(hotel.info)"></div>
                    </div>
                </div>
                <button class="answer-action-btn" v-show="content.hotelCompleted" @click="handleActionClick('hotel', index)">
                    <span class="btn-icon">🏨</span>
                    <span class="btn-text">操作</span>
                </button>
            </div>
            <div data-dynamic-item class="answer-area-container" v-show="'food' in content">
                <div class="loading-indicator" v-show="!content.foodCompleted">
                    <div class="spinner"></div>
                    <span class="loading-text">处理中，请稍候...</span>
                </div>
                <div class="answer-area">
                    <div class="answer-area" v-html="this.parseMarkdown('**美食推荐:**\n\n')"></div>
                    <div class="answer-area" v-for="(food, foodIndex) in content.food" :key="'food-' + foodIndex">
                        <span class="icon-ori">🍜</span>
                        {{ food.name }}
                        <img :src="food.url" alt="美食图片" class="food-image">
                        <div class="answer-area" v-html="this.parseMarkdown(food.info)"></div>
                    </div>
                </div>
                <button class="answer-action-btn" v-show="content.foodCompleted" @click="handleActionClick('food', index)">
                    <span class="btn-icon">🍜</span>
                    <span class="btn-text">操作</span>
                </button>
            </div>
            <div data-dynamic-item class="answer-area-container" v-show="'cost' in content">
                <div class="loading-indicator" v-show="!content.costCompleted">
                    <div class="spinner"></div>
                    <span class="loading-text">处理中，请稍候...</span>
                </div>
                <div class="answer-area" v-html="parseMarkdown(content.cost)"></div>
                <button class="answer-action-btn" v-show="content.costCompleted" @click="handleActionClick('cost', index)">
                    <span class="btn-icon">💰</span>
                    <span class="btn-text">操作</span>
                </button>
            </div>
            <hr class="vitepress-divider" v-show="'cost' in content">
        </div>
    </div>
</div>
.travel-form {
    max-width: 800px;
    margin: 2rem auto;
    padding: clamp(1rem, 4vw, 2rem);
    background: var(--vp-c-bg-soft);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    width: 100%;
    box-sizing: border-box;
}

.form-item {
    width: 100%;
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: var(--vp-c-text-1);
    font-size: clamp(0.8rem, 1vw, 1.2rem); /* 最小 0.8rem，最大 1.2rem，随视口宽度调整 */
}

.form-label .icon {
    display: inline-block;
}

.icon-ori {
    margin-right: 1.5rem;
    font-size: 1.5em;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.icon {
    margin: auto;
    font-size: 1.5em;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.form-input {
    width: 100%;
    padding: clamp(0.6rem, 2vw, 0.8rem) clamp(0.8rem, 3vw, 1.2rem);
    border: 2px solid var(--vp-c-brand-soft);
    border-radius: 8px;
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--vp-c-bg-soft);
    box-sizing: border-box;
}

.form-input:hover {
    border-color: var(--vp-c-brand);
    box-shadow: 0 2px 8px rgba(60, 130, 240, 0.2);
}

.form-input:focus {
    outline: none;
    border-color: var(--vp-c-brand);
    box-shadow: 0 2px 12px rgba(60, 130, 240, 0.3);
}

.option-area {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: clamp(0.5rem, 2vw, 1rem);
    margin-bottom: 1.5rem;
}

/* 日期与天数行 */
.form-row {
    display: flex;
    flex: 1 1 auto;
    min-width: 200px;
}

.vp-date-input {
    width: 100%;
    height: clamp(45px, 8vw, 50px);
    padding: clamp(0.4rem, 1.5vw, 0.5rem);
    border: 2px solid var(--vp-c-brand-soft);
    border-radius: 8px;
    background: var(--vp-c-bg-soft);
    color: var(--vp-c-text);
    font-size: clamp(0.75rem, 2vw, 0.875rem);
    box-sizing: border-box;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.vp-date-input:focus {
    outline: none;
    border-color: var(--vp-c-brand);
    box-shadow: 0 2px 12px rgba(60, 130, 240, 0.3);
}

.travel-days,
.plan-mode,
.travel-mode {
    display: flex;
    flex: 0 1 auto;
    min-width: 100px;
}

.form-day-input,
.form-plan-input,
.form-travel-input {
    width: 100%;
    height: clamp(45px, 8vw, 50px);
    padding: clamp(0.4rem, 1.5vw, 0.8rem);
    border: 2px solid var(--vp-c-brand-soft);
    border-radius: 8px;
    text-align: center;
    font-size: clamp(0.75rem, 2vw, 0.875rem);
    background: var(--vp-c-bg-soft);
    box-sizing: border-box;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-day-input:focus,
.form-plan-input:focus,
.form-travel-input:focus {
    outline: none;
    border-color: var(--vp-c-brand);
    box-shadow: 0 2px 12px rgba(60, 130, 240, 0.3);
}

.day-suffix {
    color: var(--vp-c-text-2);
    font-weight: 500;
}

/* 按钮组容器 */
.button-group {
    display: flex;
    gap: 1rem;
    width: 100%;
    align-items: center;
}

.btn-planning,
.btn-reset {
    /* 基础结构 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    padding: 0.9rem 1.8rem;
    border-radius: 10px;
    font-family: var(--vp-font-family-base);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    font-size: 1rem;

    /* 图标样式 */
    .icon {
        font-size: 1.2em;
        transition: transform 0.3s ease;
        filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1));
    }

    /* 文字装饰线 */
    .btn-text {
        position: relative;

        &::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 100%;
            height: 2px;
            background: currentColor;
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
    }

    /* 焦点状态 (Accessibility) */
    &:focus-visible {
        outline: 2px solid var(--vp-c-brand-light);
        outline-offset: 2px;
    }
}

/* 主要按钮样式 */
.btn-planning {
    flex: 1;
    background: var(--vp-button-brand-bg);
    color: var(--vp-button-brand-text);
    border: 2px solid var(--vp-button-brand-border);
    box-shadow:
        var(--vp-shadow-1),
        inset 0 1px 1px rgba(255, 255, 255, 0.15);

    /* 悬停效果 */
    &:hover {
        transform: translateY(-2px);
        box-shadow:
            var(--vp-shadow-2),
            inset 0 1px 2px rgba(255, 255, 255, 0.2);

        .icon {
            transform: translateY(-2px) rotate(-15deg);
        }

        .btn-text::after {
            transform: scaleX(1);
        }
    }

    /* 点击效果 */
    &:active {
        transform: translateY(1px);
        box-shadow:
            var(--vp-shadow-1),
            inset 0 2px 3px rgba(0, 0, 0, 0.1);
    }

    /* 流光效果 */
    &::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg,
                transparent 25%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 75%);
        animation: shine 3s infinite linear;
        opacity: 0.3;
    }
}

/* 重置按钮样式 */
.btn-reset {
    flex: 0 0 auto;
    min-width: 100px;
    background: var(--vp-c-bg-soft);
    color: var(--vp-c-text-2);
    border: 2px solid var(--vp-c-divider);
    box-shadow: var(--vp-shadow-1);

    /* 悬停效果 */
    &:hover {
        background: var(--vp-c-bg-mute);
        color: var(--vp-c-text-1);
        border-color: var(--vp-c-brand-soft);
        transform: translateY(-1px);
        box-shadow: var(--vp-shadow-2);

        .icon {
            transform: rotate(180deg);
        }

        .btn-text::after {
            transform: scaleX(1);
        }
    }

    /* 点击效果 */
    &:active {
        transform: translateY(0);
        box-shadow: var(--vp-shadow-1);
    }
}

@keyframes shine {
    100% {
        transform: translateX(100%);
    }
}

.answer-area {
    white-space: pre-wrap;
    /* 保留换行符 */
    /* word-break: break-word; */
    /* 处理长单词/URL换行 */
    /* min-height: 1em; */
    /* 最小高度保障 */
    max-width: 800px;
    /* 按需设置宽度 */

    /* 文字系统 */
    font-family: var(--vp-font-family-base);
    /* 继承 Vitepress 基础字体 */
    /* font-size: 1.5vw; */
    line-height: 1.7;
    /* 专业文档行高 */
    /* color: var(--vp-c-text-1); */
    /* 使用 Vitepress 文本色变量 */

    /* 背景与边框
    background: var(--vp-c-bg-soft);
    /* 柔和背景适配深色模式
    /* border: 1px solid var(--vp-c-divider-light);
    /* 动态分割线颜色
    border-radius: 16px;
    /* 现代圆角值
    box-shadow: 0 1px 2px var(--vp-c-divider-light); */
    /* 微妙阴影 */

    /* 交互细节 */
    transition:
        border-color 0.25s,
        background-color 0.25s,
        box-shadow 0.25s;
    /* 多属性过渡动画 */

    /* 内容排版增强 */
    white-space: pre-wrap;
    word-break: break-word;
    tab-size: 2;
}

.answer-area a {
    color: var(--vp-c-brand);
    text-decoration: none;
    border-bottom: 1px solid var(--vp-c-brand);
    transition: border-color 0.25s;
}

.answer-area a:hover {
    transform: scale(1.2) !important;
    transition: transform 0.5s ease !important;
}

.hotel-link {
    margin-bottom: 2rem;
}

.view-image,
.food-image {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin: 0.5rem 0;
    object-fit: cover;
}

.view-image:hover,
.food-image:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 地图容器响应式样式 */
.planning-box {
    margin: 1.5rem 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.map-container {
    width: 100%;
    height: clamp(300px, 50vh, 500px);
    min-height: 250px;
    background: var(--vp-c-bg-soft);
    border-radius: 12px;
}

.vitepress-divider {
    border: 0;
    height: 1px;
    background: var(--vp-c-divider);
    /* 使用主题变量 */
    margin: 32px auto;
    position: relative;
    width: 85%;
}

/* 添加渐变效果增强设计感 */
.vitepress-divider::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg,
            transparent 0%,
            var(--vp-c-brand) 50%,
            transparent 100%);
    top: -1px;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .container {
        padding: 16px;
    }

    .vitepress-divider {
        margin: 24px auto;
        width: 92%;
    }
}

.loading-circle {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #333;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Answer Area Container with Action Button */
.answer-area-container {
    position: relative;
    margin-bottom: 1rem;
    padding: 3rem 1rem 1rem 1rem; /* 顶部留出空间给加载指示器 */
    background: var(--vp-c-bg-soft);
    border-radius: 12px;
    border: 1px solid var(--vp-c-divider-light);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.answer-area-container:hover {
    border-color: var(--vp-c-brand-soft);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* Action Button Styles */
.answer-action-btn {
    position: absolute;
    bottom: 12px;
    right: 12px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    font-family: var(--vp-font-family-base);
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

    /* VitePress 风格的颜色 */
    background: var(--vp-c-brand-soft);
    color: var(--vp-c-brand-1);
    border: 1px solid var(--vp-c-brand-soft);

    /* 微妙的阴影 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    /* 动画效果 */
    opacity: 0;
    transform: translateY(10px) scale(0.95);
    animation: slideInUp 0.4s ease-out forwards;
}

.answer-action-btn:hover {
    background: var(--vp-c-brand);
    color: var(--vp-c-white);
    border-color: var(--vp-c-brand);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.answer-action-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.answer-action-btn:focus-visible {
    outline: 2px solid var(--vp-c-brand);
    outline-offset: 2px;
}

/* Button Icon and Text */
.answer-action-btn .btn-icon {
    font-size: 1em;
    line-height: 1;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.answer-action-btn .btn-text {
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* Slide in animation */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Dark mode adjustments */
.dark .answer-action-btn {
    background: var(--vp-c-brand-dimm);
    color: var(--vp-c-brand-light);
    border-color: var(--vp-c-brand-dimm);
}

.dark .answer-action-btn:hover {
    background: var(--vp-c-brand);
    color: var(--vp-c-white);
    border-color: var(--vp-c-brand);
}

/* Loading Indicator Styles */
.loading-indicator {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--vp-c-bg);
    border: 1px solid var(--vp-c-divider);
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--vp-c-text-2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;

    /* 入场动画 */
    animation: fadeInScale 0.3s ease-out;
}

/* 旋转的圆圈 */
.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--vp-c-divider);
    border-top: 2px solid var(--vp-c-brand);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 加载文本 */
.loading-text {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--vp-c-text-2);
    white-space: nowrap;
}

/* 旋转动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 入场动画 */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 深色模式适配 */
.dark .loading-indicator {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-divider);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark .spinner {
    border-color: var(--vp-c-divider);
    border-top-color: var(--vp-c-brand);
}

.dark .loading-text {
    color: var(--vp-c-text-2);
}

/* 响应式调整 */
@media (max-width: 640px) {
    .loading-indicator {
        top: 8px;
        left: 8px;
        padding: 4px 8px;
        font-size: 0.75rem;
        gap: 6px;
    }

    .spinner {
        width: 14px;
        height: 14px;
        border-width: 1.5px;
    }

    .loading-text {
        font-size: 0.75rem;
    }

    .answer-action-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
        bottom: 8px;
        right: 8px;
    }

    .answer-action-btn .btn-icon {
        font-size: 0.9em;
    }
}

/* 滚动状态指示器 */
.scroll-status-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--vp-c-bg);
    border: 1px solid var(--vp-c-brand);
    border-radius: 12px;
    padding: 12px 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
}

.status-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-icon {
    font-size: 1.2em;
    line-height: 1;
}

.status-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--vp-c-text-1);
    white-space: nowrap;
}

/* 滑入动画 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 深色模式适配 */
.dark .scroll-status-indicator {
    background: var(--vp-c-bg-soft);
    border-color: var(--vp-c-brand);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

/* 响应式断点优化 */

/* 超小屏幕设备 (手机竖屏) */
@media (max-width: 480px) {
    .travel-form {
        margin: 1rem auto;
        padding: 1rem;
        border-radius: 12px;
    }

    .option-area {
        flex-direction: column;
        gap: 1rem;
    }

    .form-row,
    .travel-days,
    .plan-mode,
    .travel-mode {
        width: 100%;
        min-width: unset;
    }

    .form-label {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .icon-ori {
        margin-right: 0.75rem;
        font-size: 1.2em;
    }

    .view-image,
    .food-image {
        max-width: 100%;
        border-radius: 8px;
    }

    .map-container {
        height: clamp(250px, 40vh, 350px);
        border-radius: 8px;
    }

    .answer-area {
        font-size: 0.875rem;
        line-height: 1.6;
    }

    /* 按钮组移动端优化 */
    .button-group {
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn-planning,
    .btn-reset {
        width: 100%;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .btn-reset {
        min-width: unset;
    }
}

/* 小屏幕设备 (手机横屏/小平板) */
@media (max-width: 640px) {
    .travel-form {
        margin: 1.5rem auto;
        padding: 1.5rem;
    }

    .option-area {
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .form-row {
        flex: 1 1 100%;
        min-width: unset;
    }

    .travel-days,
    .plan-mode,
    .travel-mode {
        flex: 1 1 calc(33.333% - 0.5rem);
        min-width: 90px;
    }

    .answer-area-container {
        padding-top: 2.5rem;
        margin-bottom: 1rem;
    }

    .scroll-status-indicator {
        top: 10px;
        right: 10px;
        padding: 8px 12px;
    }

    .status-text {
        font-size: 0.8rem;
    }

    .map-container {
        height: clamp(280px, 45vh, 400px);
    }
}

/* 中等屏幕设备 (平板) */
@media (min-width: 641px) and (max-width: 1024px) {
    .travel-form {
        max-width: 700px;
        padding: 1.75rem;
    }

    .option-area {
        gap: 0.75rem;
    }

    .form-row {
        flex: 1 1 auto;
        min-width: 180px;
    }

    .travel-days,
    .plan-mode,
    .travel-mode {
        flex: 0 1 auto;
        min-width: 110px;
    }

    .map-container {
        height: clamp(350px, 45vh, 450px);
    }
}

/* 大屏幕设备优化 */
@media (min-width: 1200px) {
    .travel-form {
        max-width: 900px;
        padding: 2.5rem;
    }

    .answer-area {
        font-size: 1rem;
        line-height: 1.8;
    }

    .view-image,
    .food-image {
        max-width: 450px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .form-input,
    .vp-date-input,
    .form-day-input,
    .form-plan-input,
    .form-travel-input {
        min-height: 44px; /* iOS 推荐的最小触摸目标 */
        font-size: 16px; /* 防止 iOS Safari 缩放 */
    }

    .btn-planning {
        min-height: 48px;
        padding: 1rem 2rem;
    }

    .answer-action-btn {
        min-height: 40px;
        padding: 10px 14px;
    }

    /* 移除悬停效果，因为触摸设备没有真正的悬停 */
    .view-image:hover,
    .food-image:hover {
        transform: none;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .form-input,
    .vp-date-input,
    .form-day-input,
    .form-plan-input,
    .form-travel-input {
        border-width: 3px;
    }

    .answer-area-container {
        border-width: 2px;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .view-image,
    .food-image {
        transition: none;
    }

    .view-image:hover,
    .food-image:hover {
        transform: none;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    .travel-form {
        margin: 1rem auto;
        padding: 1rem;
    }

    .map-container {
        height: clamp(200px, 35vh, 300px);
    }

    .scroll-status-indicator {
        top: 5px;
        right: 5px;
        padding: 6px 10px;
        font-size: 0.75rem;
    }
}
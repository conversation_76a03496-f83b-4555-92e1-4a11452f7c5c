<template>
  <div class="home-container">
    <ClientOnly>
      <MapContainer v-if="isMounted"/>
    </ClientOnly>
  </div>
</template>

<style scoped>
.showcase-button-container {
  margin-top: 3rem;
  text-align: center;
}

.showcase-button {
  display: inline-block;
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.button-image {
  width: 320px;
  height: 180px;
  object-fit: cover;
  border: 2px solid var(--vp-c-brand);
  border-radius: 16px;
}

.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.hover-overlay span {
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.showcase-button:hover {
  transform: translateY(-5px);
}

.showcase-button:hover .hover-overlay {
  opacity: 1;
}

@media (max-width: 640px) {
  .button-image {
    width: 280px;
    height: 160px;
  }

  .showcase-button-container {
    margin-top: 2rem;
  }
}
</style>

<script setup>
import { ref, onMounted } from 'vue'
import { defineAsyncComponent } from 'vue'

const isMounted = ref(false)
const scrollController = ref(null)

const MapContainer = defineAsyncComponent(() =>
  import('../components/MapContainer.vue')
)

onMounted(() => {
  isMounted.value = true
})
</script>